{"Common": {"contactUs": "Susisiekite", "getStarted": "<PERSON><PERSON><PERSON><PERSON>", "learnMore": "Suž<PERSON>ti daugiau", "bookMeeting": "Rezervuoti susitikimą", "viewPricing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ourServices": "Mūsų paslaugos", "talkToHuman": "Kalbėti su žmogumi"}, "Navigation": {"home": "Pradžia", "about": "Apie mus", "services": "Paslaugos", "testimonials": "Atsiliepimai", "faq": "DUK", "contact": "Kontaktai", "approach": "<PERSON><PERSON><PERSON>", "websiteDevelopment": "Svetainių kūrimas", "chatbotIntegration": "Chatbot integracija", "menu": "<PERSON><PERSON>", "menuSubtitle": "Pradėkime aptardami j<PERSON> tikslus, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir pageidavimus.", "conceptIdeation": "Koncepcija ir idėjos", "conceptDescription": "<PERSON>s s<PERSON> ir kuriame detalų jū<PERSON>ų svetainė<PERSON>.", "projectKickoff": "Projekto pradžia", "freeConsultation": "Nemokama konsultacija", "websiteDevelopmentDesc": "<PERSON><PERSON>, sukurtos naudojant šiuolaikines technologijas", "chatbotIntegrationDesc": "Dirbtinio intelekto chatbotai klientų įtraukimui"}, "Footer": {"references": "<PERSON><PERSON><PERSON><PERSON>", "services": "Paslaugos", "socialNetworks": "Socialiniai tinklai", "contact": "Kontaktai", "approach": "<PERSON><PERSON><PERSON>", "aboutUs": "Apie mus", "testimonials": "Atsiliepimai", "faq": "DUK", "contactUs": "Kontaktai", "websiteDevelopment": "Svetainių kūrimas", "chatbotIntegration": "Pokalbių asistento integracija", "linkedin": "LinkedIn", "newsletter": "Prenumeruokite mūsų naujienlaiškį", "copyright": "© 2025 UpZera. Visos teisės saugomos."}, "Chatbot": {"viewPricing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "viewPricingDesc": "Peržiūrėkite mūsų kainas ir paketus", "bookMeeting": "Rezervuoti susitikimą", "bookMeetingDesc": "Suplanuokite nemokamą konsultaciją", "ourServices": "Mūsų paslaugos", "ourServicesDesc": "Sužinokite apie mūsų sprendimus", "talkToHuman": "Kalbėti su žmogumi", "talkToHumanDesc": "Gaukite individualų pagalbą", "welcomeMessage": "Sveiki! 👋 Aš esu UpZera asistentas.", "welcomeDescription": "<PERSON><PERSON> k<PERSON> i<PERSON> skait<PERSON>us sprendimus, kurie stumia verslą į priekį. A<PERSON> ką norėtum<PERSON> su<PERSON>?", "onlineStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offlineStatus": "Atsijungęs", "replyPlaceholder": "Atsakyti UpBot", "quickActionsTitle": "Apie ką norėtumėte sužinoti?", "quickActionServices": "🚀 <PERSON><PERSON><PERSON><PERSON> paslaugos", "quickActionPricing": "💰 Kainos", "quickActionBookMeeting": "📅 Rezervuoti susitikimą", "quickActionPortfolio": "💼 Portfe<PERSON>", "quickActionServicesMessage": "Papasakokite apie savo paslaugas", "quickActionPricingMessage": "<PERSON><PERSON>os jū<PERSON> kaino<PERSON>?", "quickActionConsultationMessage": "<PERSON>iu rezervuoti konsultaciją", "quickActionPortfolioMessage": "Parodykite savo ankstesnius darbus", "serviceOptionsTitle": "Pasirinkite paslaugą, apie kurią norite sužinoti daugiau:", "serviceWebDevelopment": "Pilno ciklo s<PERSON>ain<PERSON> kūrimas", "serviceChatbots": "Chatbot potencialių klientų pritraukimas", "serviceWebDevelopmentMessage": "Papasakokite apie savo svetainių kūrimo paslaugas", "serviceChatbotsMessage": "<PERSON><PERSON> apie jūsų chatbot paslaugas", "leadFormTitle": "Kontaktinė informacija", "leadFormName": "Vardas", "leadFormEmail": "El. <PERSON>", "leadFormSubmit": "Pat<PERSON><PERSON><PERSON>", "leadFormSubmitting": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "leadFormErrorEmail": "Prašome įvesti teisingą el. pašto adresą", "leadFormErrorName": "Prašome įvesti savo vardą", "leadFormErrorSubmit": "Nepavyko pateikti. Prašome bandyti vėliau.", "leadFormSuccess": "<PERSON><PERSON><PERSON><PERSON> {name}! Mūsų komanda netrukus su jumis susisieks.", "bookingMessage": "Puiku! Mielai padėsiu jums suplanuoti konsultaciją. Leiskite parodyti mūsų rezervavimo kalendorių:", "servicesMessage": "Puiku! Štai mūsų pagrindinės paslaugos. Spustelėkite bet kurią paslaugą, kad su<PERSON>te daugiau:", "supportTicketStart": "Mielai padėsiu jums susisiekti su mūsų komanda! 🤝\n\nPadėsiu sukurti pagalbos užklausą, kad mūsų komanda galėtų suteikti reikiamą pagalbą.\n\nPirmiausia, ar galėtumėte pasakyti savo **vardą**?", "supportTicketNamePrompt": "Puiku! <PERSON><PERSON>, ar gal<PERSON>te pateikti savo **el. pa<PERSON><PERSON>**?", "supportTicketEmailPrompt": "Puiku! Dabar aprašykite problemą ar klausim<PERSON>, su kuriuo nor<PERSON>, jog mūs<PERSON> komanda padėtų:", "supportTicketConfirm": "Ačiū! Leiskite patvirtinti jūsų pagalbos užklausos duomenis:\n\n**Vardas:** {name}\n**El. pa<PERSON>:** {email}\n**Problema:** {description}\n\nAr turiu sukurti šią pagalbos užklausą?", "supportTicketSuccess": "Puiku! 🎉 Jūsų pagalbos užklausa sėkmingai sukurta.\n\n**Užklausos duomenys:**\n• **Vardas:** {name}\n• **El. pa<PERSON>:** {email}\n• **Problema:** {description}\n\n📧 **Kiti <PERSON>:**\n• Mūsų komanda peržiūrės jūsų užklausą\n• Atsakymą gausite per 24 valandas\n• Susisieksime nurodytu el. pašto adresu\n\nA<PERSON>, kad kreip<PERSON>s į UpZera!", "supportTicketError": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bet kilo problema kuriant jūsų pagalbos užklausą. Prašome bandyti dar kartą arba <NAME_EMAIL>.", "supportTicketRestart": "Gerai! Ar norėtumėte pradėti iš naujo su pagalbos užklausos informacija?", "supportTicketRestartConfirm": "Puiku! Pradėkime iš naujo. Ar <PERSON> pasakyti savo **vardą**?", "perfectConfirmDetails": "Puiku! ✅ Leiskite patvirtinti jūsų duomenis:", "nameLabel": "Vardas:", "emailLabel": "El. p<PERSON>:", "isInformationCorrect": "Ar ši informacija teisinga?", "perfectProvideDescription": "Puiku! 📧 Dabar prašome pateikti **išsamų aprašymą** problemos ar klausimo, su kuriuo susiduriate.", "tipMoreDetails": "💡 **Patarimas:** Kuo daugiau detalių pateiksite, tuo geriau gal<PERSON>sime jums padėti!", "thankYouDetailedDescription": "Ač<PERSON>ū už išsamų aprašymą! 📝", "confirmSupportTicketDetails": "Leiskite patvirtinti jūsų pagalbos užklausos duomenis:", "problemLabel": "Problema:", "shouldCreateSupportTicket": "Ar turiu sukurti pagalbos užklausą su šia informacija?", "supportTicketCreatedSuccess": "Puiku! ✅ Sukūriau jums pagalbos užklausą **{ticketNumber}**.", "nextStepsTitle": "🔍 **<PERSON><PERSON>:**", "teamWillReview": "• Mūsų komanda peržiūrės jūsų užklausą", "responseWithin24Hours": "• Atsakysime per 24 valandas", "checkEmailForUpdates": "• Tikrinkite el. paštą dėl atnaujinimų", "thankYouForContacting": "<PERSON><PERSON><PERSON><PERSON>, kad <PERSON> į UpZera!", "confirmDetailsPrompt": "Puiku! 🎉 Sujungiu jus su mūsų komanda.\n\n📞 **Kiti <PERSON>i:**\n• Kas nors netrukus su jumis susisieks\n• Susisieksime nurodytu el. pa<PERSON>to ad<PERSON>, kad pasirinkote UpZera!", "reenterDetailsPrompt": "Gerai! Ar <PERSON> iš naujo įvesti savo duomenis?", "invalidEmailMessage": "Tai nepanašu į teisingą el. pašto adres<PERSON>. <PERSON>r gal<PERSON>ėte pateikti teisingą el. paštą?", "pleaseConfirmMessage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei informacija <PERSON>, arb<PERSON> 'ne', jei norite keist<PERSON>.", "pleaseConfirmReenterMessage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei norite iš naujo įvesti duomenis, arba 'ne', jei norite daryti ką nor<PERSON> kita.", "pleaseConfirmTicketMessage": "<PERSON><PERSON><PERSON><PERSON> atsa<PERSON>ti 'taip', kad sukurtum<PERSON>te pagalbos užklaus<PERSON>, arba 'ne', jei norite keist<PERSON>.", "pleaseConfirmRestartMessage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei norite prad<PERSON> i<PERSON>, arba 'ne', jei norite daryti k<PERSON> nor<PERSON> kita.", "endConversationConfirm": "<PERSON><PERSON><PERSON><PERSON>, jog norite baigti pokalbį. Ar tikrai jums nereikia jokios kitos pagalbos šiandien?", "endConversationGoodbye": "Buvo malonu su jumis kalb<PERSON>! Drąsiai kreipkitės bet kada, jei tur<PERSON>site daugiau klausimų. Geros dienos! 👋", "continueConversation": "Puiku! <PERSON><PERSON> čia, kad <PERSON>. A<PERSON> ką norėtumėte sužinoti daugiau?", "anythingElseYes": "Puiku! Apie ką dar norėtumė<PERSON> sužinoti?", "anythingElseNo": "Suprantu! <PERSON><PERSON><PERSON> bai<PERSON> pokalbį, ar tikrai jums nereikia jokios kitos pagalbos šiandien? 🤔", "helpMenuResponses": ["<PERSON><PERSON>i padėsiu jums! <PERSON><PERSON> pagrindin<PERSON>i būdai, ka<PERSON> galiu pad<PERSON>:", "Padėkime rasti, ko ieškote! Š<PERSON> pagrindinė<PERSON> pas<PERSON>, su kuriomis galiu pad<PERSON>:", "<PERSON><PERSON>, kad pad<PERSON>! Štai pagrindinės s<PERSON>, kuriose galiu suteikti pagalbą:", "Puikus klausimas! Leiskite parodyti pagrin<PERSON> bū<PERSON>, ka<PERSON> galiu pad<PERSON>:", "Mielai pad<PERSON>u! <PERSON><PERSON> pagrindinės s<PERSON>, kuriose gal<PERSON> pad<PERSON>:", "Jokių problemų! Štai pagrindinės paslau<PERSON>, su kuriomis galiu pad<PERSON>:", "Esu pasiruoš<PERSON> padėti! Štai pagrindiniai būdai, kaip galiu padėti:", "Leiskite nukreipti jus į tinkamą vietą! Štai pagrindiniai variantai:"], "followUpPricing": "<PERSON><PERSON> <PERSON><PERSON><PERSON>te sužinoti daugiau apie konkrečių paslaugų kainas?", "followUpServices": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sužinoti daugiau apie mūsų kitas paslaugas?", "bookingYesResponse": "Puiku! Norėdami rezervuoti susitikimą, prašome nurodyti pageidaujamą datą ir laiką, arba galite patikrinti mūsų rezervavimo kalendorių dėl galimybių.", "aiErrorMessage": "Šiuo metu turiu problemų su ryšiu. Pabandykite paklausti apie mūsų paslaugas arba rezervuokite nemokamą konsultaciją!", "aiErrorFallback": "Leiskite sujungti jus su mūsų komanda! Galite rezervuoti nemokamą konsultaciją arba rašyti mums el. paštu <EMAIL>.", "fallbackHelpMessage": "<PERSON><PERSON>i padėsiu jums! Leiskite parodyti, su kuo galiu padėti:", "restartConversation": "Pradėti naują pokalbį", "conversationEnded": "Pokalbis baigtas", "mainMenuPricesMessage": "<PERSON><PERSON>ti apie jūsų kainas", "mainMenuBookingMessage": "Noriu rezervuoti susitikimą", "mainMenuServicesMessage": "Papasakokite apie savo paslaugas", "mainMenuHumanMessage": "Reikia kalbėti su žmogumi", "mainMenuGreeting": "<PERSON><PERSON> galiu jums <PERSON>dien pad<PERSON>?", "wouldYouLikeToKnowMoreServices": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sužinoti daugiau apie mūsų kitas paslaugas?", "wouldYouLikeToKnowMorePricing": "<PERSON><PERSON> <PERSON><PERSON><PERSON>te sužinoti daugiau apie konkrečių paslaugų kainas?", "greatToBookMeeting": "Puiku! Norėdami užsisakyti susitikimą, prašome nurodyti pageidaujamą datą ir laiką arba galite patikrinti mūsų rezervacijos kalendorių.", "makeSureUnderstandCorrectly": "<PERSON><PERSON>, kad te<PERSON>ai suprantu. Ar jums reikia dar kokios nors pagalbos šiandien, ar esate pasirengę baigti pokalbį?", "happyToHelpMainWays": "Mielai jums padėsiu! Štai pagrindiniai būdai, kaip galiu jums padėti:", "letMeHelpFindServices": "Leiskite padėti jums rasti tai, ko ieškote! <PERSON><PERSON> pagrin<PERSON> p<PERSON>, su kuriomis galiu padėti:", "hereToHelpMainAreas": "<PERSON><PERSON>, kad pad<PERSON>! Štai pagrindinės s<PERSON>, kuriose galiu suteikti pagalbą:", "greatQuestionMainWays": "Puikus klausimas! Leiskite parodyti pagrin<PERSON> bū<PERSON>, ka<PERSON> galiu pad<PERSON>:", "loveToAssistKeyAreas": "Mielai pad<PERSON>u! <PERSON><PERSON> pagrindinės s<PERSON>, kuriose gal<PERSON> pad<PERSON>:", "noProblemMainServices": "Jokių problemų! Štai pagrindinės paslau<PERSON>, su kuriomis galiu pad<PERSON>:", "readyToHelpPrimaryWays": "Esu pasirengęs padėti! Štai pagrindiniai būdai, kaip galiu jums padėti:", "letMeGuideMainOptions": "Leiskite nukreipti jus į tinkamą vietą! Štai pagrindiniai variantai:", "understandEndConversation": "<PERSON><PERSON><PERSON><PERSON>, kad norite baigti pokalbį. Ar tikrai jums nereikia jokios kitos pagalbos šiandien?", "happyToHelpShowAssist": "<PERSON><PERSON>i padėsiu jums! Leiskite parodyti, su kuo galiu padėti:", "greatWhatElseKnow": "Puiku! Apie ką dar norėtumė<PERSON> sužinoti?", "understandBeforeEndChat": "Suprantu! <PERSON><PERSON><PERSON> bai<PERSON> pokalbį, ar tikrai jums nereikia jokios kitos pagalbos šiandien? 🤔", "invalidEmailAddress": "Tai nepanašu į teisingą el. pašto adres<PERSON>. <PERSON>r gal<PERSON>ėte pateikti teisingą el. paštą?", "greatConnectingTeam": "Puiku! 🎉 Sujungiu jus su mūsų komanda.\n\n📞 **Kiti <PERSON>i:**\n• Kas nors netrukus su jumis susisieks\n• Susisieksime nurodytu el. pa<PERSON>to ad<PERSON>, kad pasirinkote UpZera!", "noProblemReenterDetails": "Gerai! Ar <PERSON> iš naujo įvesti savo duomenis?", "pleaseAnswerYesNoCorrect": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei informacija <PERSON>, arb<PERSON> 'ne', jei norite keist<PERSON>.", "pleaseAnswerYesNoReenter": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei norite iš naujo įvesti duomenis, arba 'ne', jei norite daryti ką nor<PERSON> kita.", "apologizeIssueCreatingTicket": "<PERSON><PERSON><PERSON><PERSON><PERSON>, bet kilo problema kuriant jūsų pagalbos užklausą. Prašome bandyti dar kartą arba <NAME_EMAIL>.", "noProblemStartOverTicket": "Gerai! Ar norėtumėte pradėti iš naujo su pagalbos užklausos informacija?", "pleaseAnswerYesNoCreateTicket": "<PERSON><PERSON><PERSON><PERSON> atsa<PERSON>ti 'taip', kad sukurtum<PERSON>te pagalbos užklaus<PERSON>, arba 'ne', jei norite keist<PERSON>.", "greatStartOverName": "Puiku! Pradėkime iš naujo. Ar <PERSON> pasakyti savo **vardą**?", "pleaseAnswerYesNoStartOver": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> 'taip', jei norite prad<PERSON> i<PERSON>, arba 'ne', jei norite daryti k<PERSON> nor<PERSON> kita.", "pleasureToChat": "Buvo malonu su jumis kalb<PERSON>! Drąsiai kreipkitės bet kada, jei tur<PERSON>site daugiau klausimų. Geros dienos! 👋", "perfectHereToHelp": "Puiku! <PERSON><PERSON> čia, kad <PERSON>. A<PERSON> ką norėtumėte sužinoti daugiau?", "letUsKnowAnythingElse": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jei re<PERSON> dar ko nors! 😊", "areYouStillThere": "Ar dar esate čia? <PERSON>su čia, jei reikia pagal<PERSON>! 😊", "beenAwayForAWhile": "<PERSON><PERSON><PERSON>, kad jūs buvote išvykę kurį laiką. Drąsiai pradėkite naują pokalbį bet kada, kai reikės pagal<PERSON>! 👋", "noProblemAnythingElse": "Jokių problemų! Ar yra dar ko nors, su kuo galiu pad<PERSON>?", "gotIt": "Supratau!", "happyToHelpLearnServices": "Mielai padėsiu jums sužinoti daugiau apie mūsų paslaugas.", "anythingElseToday": "Ar yra dar ko nors, su kuo galiu pad<PERSON>die<PERSON>?", "sureStartOverName": "Žinoma! Pradėkime iš naujo. Koks jū<PERSON> vardas?", "noWorriesAnythingElse": "Nesijaudinkite! Ar yra dar ko nors, su kuo galiu pad<PERSON>?", "noProblemAnythingElseToday": "Jokių problemų! Ar yra dar ko nors, su kuo galiu pad<PERSON> š<PERSON>dien?", "chatHasEnded": "Pokalbis baigtas", "startNewChat": "Pradėti naują pokalbį", "niceToMeetYouEmailPrompt": "<PERSON><PERSON><PERSON>, {name}! <PERSON><PERSON>, ar <PERSON>te pateikti savo el. pašto ad<PERSON>?", "thankYouEmailPrompt": "<PERSON><PERSON><PERSON><PERSON>, {name}! <PERSON><PERSON>, ar <PERSON>te pateikti savo el. pa<PERSON>to ad<PERSON>?", "fallbackGreeting": "Sveiki! Kaip galiu jums pad<PERSON>ti š<PERSON>dien?"}, "FAQ": {"title": "Dažnai užduodami klausimai", "description": "Raskite atsakymus į da<PERSON><PERSON><PERSON>ius klausimus apie mūsų paslaugas ir metodus", "q1": "Su kokiais verslais dirbate?", "a1": "Paprastai dir<PERSON> su <PERSON>, <PERSON><PERSON><PERSON><PERSON> k<PERSON> ir augančia<PERSON> vers<PERSON>, kuri<PERSON> reikia greitų, patikimų skaitmeninių įrankių — nuo nukreipimo puslapių iki sudėtingesnių dizainų. <PERSON><PERSON><PERSON><PERSON>, ar tik <PERSON>, ar <PERSON><PERSON><PERSON><PERSON><PERSON>, me<PERSON>, kad <PERSON>.", "q2": "<PERSON>ek kainuoja jūsų paslaugos?", "a2": "<PERSON><PERSON><PERSON> a<PERSON>, individualų pasiūlymą po nemokamos konsultacijos.<br /><PERSON><PERSON>s<PERSON> kainodara yra pagrįsta projektu kaina ir priklauso nuo jūsų konkrečių tikslų ir apimties. Dauguma projektų kainuoja nuo 350-500 € turint omenyje nukreipimo puslapius (landing page), apie 1 400-2 400 € pilnoms svetainėms ir 500-1 200 € atsieinama už virtualius asistentus, bet aišku tai yra tik apytiksliai skaičiai.", "q3": "<PERSON><PERSON> greitai galite pristatyti projektą?", "a3": "Terminai skiriasi priklausomai nuo sud<PERSON>, bet dauguma nukreipimo puslapių būna paruošti per kelias die<PERSON>, o pilni projektai paprastai užtrunka 2-4 savaites. Duosime aiškų terminą prieš pradėdami — ir jo laik<PERSON>.", "q4": "Ar teikiate nuolatinę pagalbą?", "a4": "Absoliučiai - bet tik tada, kai jums to reikia. Esame čia ir po paleidimo: d<PERSON><PERSON> pat<PERSON>, atnaujinimų ar patobulinim<PERSON>. Taip pat <PERSON>ten<PERSON><PERSON>, kad turi<PERSON> k<PERSON> - tekstai, nuotraukos - būtų lengvai prieinamas net ir mažiau techniniam vartotojui. Svarbiausia - tiesiog susisiekite, ir mes padėsime.", "q5": "Kuo skiriates nuo kitų svetainių kūrimo komandų?", "a5": "Esame inžinieriai iš pašaukimo - apsėsti a<PERSON>škiais, veikianč<PERSON><PERSON> sprendima<PERSON>. Skirtingai nuo dauge<PERSON>, kurie remiasi šablonais ir lėtai taikosi prie pokyčių, mes pasitelkiame dirbtinio intelekto darbo sraut<PERSON>, kad greitai pristatytume aukščiausios kokybės rezultatą. Kiekvieną sprendimą kuriame tik pagal jūsų verslo poreikius - ne pagal šabloną.", "q6": "<PERSON><PERSON>?", "a6": "Paprasta — tiesiog rezervuokite nemokamą konsultaciją. Kalbėsimės apie jūsų tikslus, įvertinsime jūsų poreikius ir duosime individualų veiksmų planą.", "q7": "<PERSON><PERSON> t<PERSON>kote sąska<PERSON> ir mokėjimus?", "a7": "<div class=\"space-y-4\"><p><PERSON>k<PERSON>ji<PERSON> darome paprastus ir skaidrius:</p><ul class=\"list-disc list-inside space-y-2 pl-4\"><li><b>Nemokamas koncepcijos peržiūra</b> - <PERSON><PERSON><PERSON><PERSON> maž<PERSON> dizaino per<PERSON>, kad įsitikintume, jog tinkame</li><li><b>Sutar<PERSON> pasirašymas</b> - <PERSON><PERSON> patinka kryptis, formalizuosime susitarimą</li><li><b>Depo<PERSON><PERSON> mok<PERSON></b> - 20% mažiems projektams arba etapais didesniems</li><li><b>Aiškios sąskaitos</b> - Pagal sutartus etapus arba galutinį pristatymą</li><li><b>Lankstūs terminai</b> - Standartiniai NET15 (užsakovas turi sumokėti pardavėjui per dienų nuo sąskaitos išrašymo) mok<PERSON><PERSON><PERSON> terminai, bet galime prisitaikyti prie jūsų poreikių</li></ul><p>Jokių staigmenų — tik aiškūs mokėjimo terminai, kurie tinka mums abiem.</p></div>", "q8": "Ko reikia projektui pradėti?", "a8": "<div class=\"space-y-4\"><p>Mūsų supaprastintas 6 žingsnių procesas:</p><ol class=\"list-decimal list-inside space-y-3 pl-4\"><li class=\"font-medium\">Pa<PERSON><PERSON><PERSON><br /><span class=\"font-normal text-purple-100/80\">Trumpas pokalbis apie jūsų verslą, tikslus ir iššūkius</span></li><li class=\"font-medium\">Koncepcija ir demonstracija<br /><span class=\"font-normal text-purple-100/80\">Pasiūlome idėjas ir, jei reikia, nemokamą demonstraciją</span></li><li class=\"font-medium\">Apimtis ir sutartis<br /><span class=\"font-normal text-purple-100/80\">Sutariame dėl darbų, kainos ir terminų, pasirašome sutartį</span></li><li class=\"font-medium\">Pradžia<br /><span class=\"font-normal text-purple-100/80\">Suplanuojame darbus ir pradedame kūrimą</span></li><li class=\"font-medium\">Vystymas<br /><span class=\"font-normal text-purple-100/80\">Kuriame etapais, dalinamės progresu ir renkame grįžtamąjį ryšį</span></li><li class=\"font-medium\">Pristatymas ir palaikymas<br /><span class=\"font-normal text-purple-100/80\">Perduodame sprendimą ir liekame pasiekiami pagalbai</span></li></ol><p>Paprasta, skaidru ir orientuota į jūsų tikslus.</p></div>", "stillHaveQuestions": "Vis dar turite klausim<PERSON>?", "stillHaveQuestionsDesc": "<PERSON><PERSON>, kad pad<PERSON> su bet kokiais klausimais apie mūsų paslaugas"}, "HomePage": {"heroTitle": "<PERSON><PERSON><PERSON> s<PERSON><PERSON><PERSON> sp<PERSON>, kurie augina jū<PERSON> versl<PERSON>", "heroHighlight": "be joki<PERSON> pastang<PERSON>", "heroSubtitle": "UpZera padeda jums augti su dirbtinio intelekto įtraukimu, kuris sujungia svetaines ir chatbotus į vieną sklandžią patirtį.", "launchWithUs": "Pradėkite su mumis", "freeConsultation": "Nemokama konsultacija", "fastTurnaround": "Greitas įgyvendinimas", "coreServicesTitle": "<PERSON><PERSON><PERSON><PERSON> pagrin<PERSON>s paslaugos ir y<PERSON>", "fullStackTitle": "Pilno ciklo s<PERSON>ain<PERSON> kūrimas", "fullStackDesc": "Nuo dizaino iki backend logikos — kuriame visiškai funkcionalias svetaines, kurios atrodo nepriekaištingai ir veikia dar geriau.", "chatbotsTitle": "Chatbotai ir potencialių klientų pritraukimas", "chatbotsDesc": "Pritraukite potencialius k<PERSON>, atsakykite akimirksniu į rūpimus klausimus su specialiai sukurtu virtualiu asistentu, kurie veikia 24/7.", "supportTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "supportDesc": "Mes neding<PERSON>me po paleidimo — es<PERSON>, kad p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, tobulintume, jei reikia hostintume ir augintume jūsų viziją kartu su jumis.", "deploymentTitle": "Žaibiškai greitas diegimas", "deploymentDesc": "Paleiskite savo sprendimus greitai su mūsų supaprastintu diegimo procesu.", "techStackTitle": "Mūsų", "techStackHighlight": "technologijų rink<PERSON>ys", "techStackDesc": "<PERSON><PERSON><PERSON><PERSON> technolog<PERSON>, kad sukurtume patiki<PERSON>, pleč<PERSON>us ir aukšto našumo internetinius sprendimus.", "whyChooseTitle": "<PERSON><PERSON><PERSON><PERSON> rinktis UpZera?", "otherSpecialists": "<PERSON><PERSON>", "limitedResultsTitle": "Riboti rezultatai ir aukš<PERSON> ka<PERSON>:", "limitedResultsDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> u<PERSON> pagrin<PERSON> sp<PERSON>, daug<PERSON><PERSON> d<PERSON><PERSON>io skiria išvaizdai nei našumui/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON> p<PERSON>.", "confusingProcessTitle": "<PERSON><PERSON><PERSON> pro<PERSON>, <PERSON><PERSON><PERSON><PERSON>myb<PERSON> p<PERSON>:", "confusingProcessDesc": "Komplikuoja su žargonu, ne<PERSON><PERSON><PERSON> kas kuria produktą, l<PERSON><PERSON> produkto perdavimai.", "oneWayCommTitle": "<PERSON><PERSON><PERSON><PERSON> bend<PERSON>:", "oneWayCommDesc": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>o <PERSON>.", "weAtUpZera": "Mes UpZera esame", "affordableTitle": "<PERSON><PERSON><PERSON><PERSON>, pritaikyti ir pilno ciklo:", "affordableDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, pilno c<PERSON>, pritaikyti jū<PERSON>ų poreikiams.", "clearProcessTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> pro<PERSON>, <PERSON><PERSON><PERSON>is p<PERSON>:", "clearProcessDesc": "<PERSON><PERSON><PERSON> m<PERSON>, p<PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON><PERSON> terminai, visada žinote statusą.", "personalTitle": "Asmeniškai ir greitai reaguojame:", "personalDesc": "Tiesioginis prieinamumas prie kūr<PERSON>, greitas bendravimas per pageidaujamus kanal<PERSON>, bendradarbiaujame ir esame skai<PERSON>.", "ctaTitle": "Turite idėją? Pasinerkime į ją kartu.", "ctaDesc": "Susisiekite su mumis, jog su<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nemo<PERSON>ą projekto konsultaciją.", "useFreeConsultation": "Pasinaudoti nemokama konsultacija", "floatingKeyword1": "KONVERSIJOMS ORIENTUOTA", "floatingKeyword2": "BE PASTANGŲ", "floatingKeyword3": "AGENTINIS-DI", "floatingKeyword4": "PRADĖK ŠIANDIEN!"}, "AboutPage": {"heroTitle": "Taigi... kas mes iš tikr<PERSON>j<PERSON>?", "heroSubtitle": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> s<PERSON>besni nei p<PERSON>seliai. <PERSON><PERSON><PERSON>, a<PERSON><PERSON><PERSON><PERSON> ir did<PERSON>u s<PERSON>.", "ourStoryTitle": "Mūsų istorija", "story1": "UpZera pradėjo veiklą 2025 metais su viena misija: kurti išgyvendinti išmanias skaitmenes <PERSON>, kurios iš tikrųjų stumia verslą į priekį — gre<PERSON>i, švariai ir tikslingai.", "story2": "<PERSON><PERSON>, k<PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, technologijomis apsė<PERSON> k<PERSON>, kurianti svetaines ir virtualius asistentus. Integruodami šių dienų dirbtinio intelekto technologijas į mūsų procesą, galime pristatyti greičiau, pigiau ir efektyviau nei daugelis specializuotų agentūrų, nekirpdami kamp<PERSON> — greitis ir patikimumas, vienas šalia kito.", "story3": "Mes jus nuvesime nuo idėjos iki paleidimo — ir kai mūsų reikia, esame pasiruo<PERSON> įsikišti ir palaikyti sklandų veikimą. Jokių dingimų. Jokių perdavimų kokiai nors beveidei pagalbos linijai. Tik tikri <PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON>, kai to reikia.", "story4": "Kiekvienas mūsų pristatomas sprendimas yra pritaikytas individualiai — jokių perkrautų šablonų ar nereikalingų dalykų. Tik iš<PERSON>ūs, tikslingi sprendimai, sukurti orientuoti į jūsų verslą, jūs<PERSON> istoriją ir jūsų kitą didįjį žingsnį.", "tagline": "✨ Pakelkite savo verslą be jokių pastangų.", "teamTitle": "Vadovaujami smalsių inžinierių", "teamSubtitle": " UpZera representuoja du aistringi inžinerijos studentai — vienas elektros inžinerijos, kitas kompiuterių mokslų. Mes sujungiame techninį gilumą su dizaino jausmu, kad sukurtume patiki<PERSON>, efektyvius ir šiuolaikiškus sprendimus.", "edgarasRole": "CEO", "edgarasDesc": "Elektros inžinerijos studentas su aistra švariam kodui ir efektyvioms sistemoms.", "valuesTitle": "<PERSON>o mes tikime", "valuesInteractiveTitle": "<PERSON><PERSON> ve<PERSON> yra visk<PERSON>, k<PERSON>, pag<PERSON><PERSON>.", "honestyTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "honestyDesc": "Sakome ką turime omenyje ir kuriame ką pažadame.", "responsibilityTitle": "Atsakomybė", "responsibilityDesc": "Jūsų projektas yra mūsų projektas. Rūpinamės juo taip pat r<PERSON>.", "clientCareTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "clientCareDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>, antra kuria<PERSON>. Jūsų poreikiai visada pirmoje vietoje.", "trustworthinessTitle": "Patikimumas", "trustworthinessDesc": "<PERSON><PERSON><PERSON>, kad pasi<PERSON> yra u<PERSON>. Esame čia ilgam.", "servicesTitle": "<PERSON><PERSON>o c<PERSON>, <PERSON>as augimui", "webDevTitle": "Svetainių kūrimas", "webDesign": "Svetainių dizainas", "uiUxImplementation": "UI/UX įgyvendinimas", "backendTitle": "Backend'as ir automat<PERSON><PERSON><PERSON>", "backendLogic": "Backend logika", "processAutomation": "Procesų automatizavimas", "chatbotsTitle": "Chatbotai ir potencialių klientų užtikrinimo įrankiai", "aiIntegrations": "DI integracijos", "leadGeneration": "Potencialių klientų pritraukimas", "integrationsTitle": "<PERSON><PERSON><PERSON> integra<PERSON>", "apis": "API", "calendarBooking": "Ka<PERSON><PERSON><PERSON>us/rezervavimo įrankiai", "performanceTitle": "Našumas ir SEO", "optimization": "Optimizavimas", "searchVisibility": "<PERSON><PERSON><PERSON><PERSON>", "supportTitle": "Nuolat<PERSON>ė pagalba", "maintenance": "Priežiūra", "updatesImprovements": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir pat<PERSON>i", "ctaFinalTitle": "Gana apie mus — kalbėkime apie jus", "ctaFinalDesc": "Pa<PERSON><PERSON><PERSON><PERSON> pat<PERSON>ti savo skaitmeninį įvaizdį? Pradėkime pokalbį apie jūsų verslo tikslus.", "getFreeQuote": "<PERSON><PERSON><PERSON> pasiū<PERSON>"}, "ContactPage": {"heroTitle": "Nedvejokite susisiekti", "heroSubtitle": "<PERSON><PERSON><PERSON> visk<PERSON>, kad atsakytume į visus jūsų klausimus ir suteiktume jums mūsų paslaugas", "email": "El. <PERSON>", "phone": "Telefonas", "address": "<PERSON><PERSON><PERSON>", "contactUs": "<PERSON><PERSON><PERSON><PERSON> forma", "name": "Vardas", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> vardas", "emailPlaceholder": "<EMAIL>", "service": "Paslauga", "message": "Žinutė", "messagePlaceholder": "Jūsų ž<PERSON>", "sending": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "contact": "<PERSON><PERSON><PERSON><PERSON>", "chatbotIntegration": "Pokalbių asistento integracija", "chatbotIntegrationDesc": "DI pokalbių sprendimai", "webDevelopment": "Svetainių kūrimas", "webDevelopmentDesc": "Individualūs svetainių sprendimai", "otherServices": "Kit<PERSON> p<PERSON>lau<PERSON>", "otherServicesDesc": "<PERSON><PERSON><PERSON>", "mvpVirtualAssistant": "MVP virtual<PERSON> asistentas", "mvpVirtualAssistantDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> chatbot funkcijos jū<PERSON> verslui", "customizableAiAssistant": "Pritaikomas DI asistentas", "customizableAiAssistantDesc": "Pažangūs sprendimai su giliokomis integracijomis", "websiteEssentials": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "websiteEssentialsDesc": "Pagrindinė svetainė su esminėmis funkcijomis", "smartBusinessWebsites": "<PERSON><PERSON><PERSON> ve<PERSON>", "smartBusinessWebsitesDesc": "Papildomos funkcijos augančiam verslui", "advancedWebPlatforms": "Pažangūs internetiniai sprendimai", "advancedWebPlatformsDesc": "Sudėtingi sprendimai su individualiomis funkcijomis", "selectedServices": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "nameRequired": "Vardas yra privalomas.", "emailRequired": "El. paštas yra privalomas.", "emailInvalid": "El. paštas neteisingas.", "serviceRequired": "Prašome pasirinkti bent vieną paslaugą.", "messageRequired": "Žinutė yra privaloma.", "thankYou": "Ačiū! Jūsų žinutė išsiųsta.", "orTitle": "ARBA!", "scheduleMeeting": "Su<PERSON><PERSON><PERSON><PERSON> susiti<PERSON>", "scheduleMeetingDesc": "Rezervuokite nemokamą konsultacijos skambutį su mūsų komanda"}, "ApproachPage": {"title": "Mūsų metodas", "subtitle": "<PERSON><PERSON><PERSON><PERSON>, kuriame ir palaikome jūsų projektą kiekviename žingsnyje.", "step1Title": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "step1Description": "<PERSON><PERSON><PERSON><PERSON> trumpu poka<PERSON>, kad suprastume jūs<PERSON> tikslus, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir projekto viziją.", "step2Title": "2. Koncepcija ir pasirinktinė demonstracija", "step2Description": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON>, pat<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> demonstracij<PERSON>, kad įsitikintume, jog m<PERSON><PERSON><PERSON> taip pat prieš t<PERSON>.", "step3Title": "3. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ir m<PERSON>", "step3Description": "Apibrėžiame projekto apimtį, terminus ir kainodarą. Susi<PERSON><PERSON> pasirašome sutartį ir paimame depozitą.", "step4Title": "4. <PERSON><PERSON><PERSON><PERSON>", "step4Description": "<PERSON> v<PERSON>, p<PERSON><PERSON><PERSON>. Suplanuojame etapus ir imam<PERSON> darbo.", "step5Title": "5. <PERSON><PERSON><PERSON><PERSON> ir grįžtamasis r<PERSON>", "step5Description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> ir renkame jū<PERSON> at<PERSON>, kad viskas būtų tinkamosie vėžėse ir tikslinga.", "step6Title": "6. <PERSON><PERSON><PERSON><PERSON><PERSON> ir p<PERSON>", "step6Description": "Užbaigiame ir perduodame jūsų projektą, siū<PERSON><PERSON> pala<PERSON> ir puslapio patal<PERSON> jei tai yra pageidautina ir galiausiai liekame prieinami ateities bendradarbiavimui."}, "Newsletter": {"emailPlaceholder": "Jūsų el. pašto adresas", "emailRequired": "El. paštas yra privalomas", "emailInvalid": "Prašome įvesti teisingą el. pašto adresą", "successMessage": "<PERSON><PERSON><PERSON><PERSON>, kad prenumeruojate mūsų naujienlaiškį!"}, "TestimonialsPage": {"heroTitle": "<PERSON><PERSON><PERSON> sėkmės istorijos", "heroSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka<PERSON> pad<PERSON> verslams transformuoti savo skaitmeninį buvimą ir pasiekti tikslus.", "featuredProject": "Pristatomas projektas: Qochi Services", "projectTitle": "<PERSON><PERSON> paleisti minimalistinį, šiuolaikišką korepetitoriaus puslapį", "testimonial1Quote": "UpZera padarė visą procesą sklandų. Svetainė yra gre<PERSON>,s<PERSON><PERSON>, ir mano <PERSON>ai m<PERSON>, kaip lengva rezervuoti sesijas.", "testimonial1Author": "— Qochi Services įkūrėjas", "checkItOut": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> →", "challengeTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "challengeDescription": "Qochi nereikėjo sudėtingų integracijų - jam pakako galimybės pateikti informaciją apie save ir p<PERSON>, kaip galima už<PERSON>akyti jo programavimo paslaugą. <PERSON><PERSON><PERSON><PERSON>, kad svet<PERSON><PERSON> atrodytų moderniai, minimalistiškai ir atitiktų jo skonį.", "deliveredTitle": "<PERSON><PERSON> prist<PERSON>", "delivered1Title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON> kra<PERSON> s<PERSON>", "delivered1Description": "<PERSON>, profesionaliu išdėstymu", "delivered2Title": "Rezervavimo integracija naudojan<PERSON>", "delivered2Description": "Leidžianti klientams be vargo rezervuoti jo sesijas", "delivered3Title": "Apgalvotas turinio iš<PERSON>", "delivered3Description": "Orientuotas į pasitikėjimą, aiškumą ir konversiją", "delivered4Title": "<PERSON><PERSON><PERSON>, elegantiš<PERSON> dizaino sistema", "delivered4Description": "<PERSON><PERSON> atspindi švietimo pobūdį Qochi prekės ženkle", "techStackTitle": "Technologijų rinkinys", "resultsTitle": "Rezultatai", "result1": "Paleista per kelias dienas", "result2Title": "Pritaikyta mobiliesiems", "result2Description": "Tobuli balai mobiliojo naudo<PERSON>mo testuose", "result3": "Sklandus rezervavimo patyrimas", "result4": "Savininkas gali viską valdyti lengvai", "clientQuoteTitle": "<PERSON><PERSON>", "clientQuote": "UpZera komanda padarė tai be pastangų. <PERSON><PERSON> t<PERSON><PERSON> su<PERSON>, ko man re<PERSON>, ir pristat<PERSON> dar geriau nei įsivaizdavau.", "ctaTitle": "Norite tapti mūsų kita sėkmės istorija?", "ctaDescription": "Paverskim jūsų idėją gyvu produktu — greitai, individualiai ir visiškai paruoštu.", "ctaButton": "Rezervuokite nemokamą konsultaciją dabar!"}, "EmailTemplates": {"contactForm": {"notificationSubject": "Na<PERSON>ja kontaktų formos užklausa!! - {service}", "notificationTitle": "Nauja kontaktų formos užklausa", "notificationDescription": "Gauta nauja užklausa iš jūsų svetainės kontaktų formos.", "contactDetails": "Kontaktinė informacija", "name": "Vardas", "email": "El. <PERSON>", "service": "Paslauga", "message": "Žinutė", "confirmationSubject": "<PERSON><PERSON><PERSON><PERSON>, kad susis<PERSON> su UpZera!", "confirmationTitle": "Ačiū už jūsų užklausą!", "confirmationGreeting": "<PERSON><PERSON><PERSON>, {name}!", "confirmationMessage": "Gavome jūsų žinutę ir susisieksime su jumis per 24 valandas. Mūsų komanda peržiūrės jūsų užklausą ir pateiks išsamų atsakymą.", "confirmationNextSteps": "<PERSON><PERSON>?", "confirmationStep1": "Mūsų komanda peržiūrės jūsų užkla<PERSON>ą", "confirmationStep2": "Susisieksime su jumis per 24 valandas", "confirmationStep3": "Aptarsime jūsų projektą ir poreikius", "immediateAssistance": "<PERSON><PERSON><PERSON> skubio<PERSON> paga<PERSON>?", "contactUsAt": "Susisiekite su mumis", "bestRegards": "Geriausių linkėjimų,", "teamSignature": "UpZera komanda"}, "newsletter": {"notificationSubject": "Nauja prenumeratos užklausa", "notificationTitle": "Nauja prenumeratos užklausa", "notificationDescription": "Naujas prenumeratorius užsiregistravo jūsų naujienlaiškiui.", "subscriberEmail": "Prenumeratoriaus el. paš<PERSON>", "confirmationSubject": "Sveiki atvykę į UpZera naujienlaiškį!", "confirmationTitle": "Sėkmingai užsiregistravote!", "confirmationMessage": "<PERSON><PERSON><PERSON><PERSON>, kad prisijungėte prie UpZera bendruomenės! Gausite naujausias naujienas apie mūsų paslaugas, technologijų tendencijas ir ekskluzyvius pasiūlymus.", "whatToExpect": "<PERSON>:", "expectation1": "Savaitiniai technologijų atnaujinimai", "expectation2": "Eksklu<PERSON><PERSON><PERSON><PERSON> p<PERSON> ir nuo<PERSON>", "expectation3": "Patarimai ir geriausios prak<PERSON>kos", "expectation4": "Ankstyvasis prieigos prie naujų funkcijų", "unsubscribeNote": "Galite bet kada atsisakyti prenumeratos paspausdami nuorodą mūsų el. laiškų apačioje."}, "supportTicket": {"notificationSubject": "🎫 Naujas palaikymo bilietas #{ticketNumber} - {name}", "notificationTitle": "Naujas palaikymo bi<PERSON>", "notificationDescription": "Gautas naujas palaikymo bilietas iš kliento.", "ticketNumber": "Bilieto numeris", "customerInfo": "Kliento informacija", "problemDescription": "<PERSON><PERSON>", "conversationHistory": "Pokalbio istorija", "noConversationHistory": "Pokalbio istorija nepateikta", "confirmationSubject": "Palaikymo bilietas sukurtas - #{ticketNumber}", "confirmationTitle": "Jūsų palaikymo bilietas sukurtas!", "confirmationGreeting": "<PERSON><PERSON><PERSON>, {name}!", "confirmationMessage": "Sėkmingai sukūrėme jūsų palaikymo bilietą. Mūsų komanda peržiūrės jūsų problemą ir susisieks su jumis kuo greičiau.", "ticketDetails": "Bilieto informacija", "yourTicketNumber": "Jūsų bilieto numeris", "status": "<PERSON><PERSON><PERSON><PERSON>", "statusOpen": "<PERSON><PERSON><PERSON>", "nextSteps": "Tolimesni žingsniai", "nextStep1": "Mūsų palaikymo komanda peržiūrės jūs<PERSON>ą", "nextStep2": "Susisieksime su jumis per 24 valandas", "nextStep3": "<PERSON><PERSON><PERSON><PERSON> kart<PERSON>, kad išspręstume problemą", "trackTicket": "Sekti bi<PERSON>tą", "trackTicketDescription": "Išsaugokite šį bilieto numerį ateities nuorodoms"}, "common": {"copyrightText": "© {year} UpZera. Visos teisės saugomos.", "logoAlt": "UpZera logotipas"}}, "WebsiteDevelopmentPage": {"heroTitle1": "<PERSON>s ne tik kuriame svet<PERSON> —", "heroTitle2": "<PERSON><PERSON> kuriame protingas skait<PERSON>ines patirtis", "heroSubtitle": "Nuo elegantiškų vieno puslapio svetainių iki sudėtingesnių platformų, mes kuriame svet<PERSON>, kurios yra greito<PERSON>, prisitaikančios ir pilnai funkcionalios.", "getStarted": "<PERSON><PERSON><PERSON><PERSON>", "packagesTitle": "Mūsų svetainių kūrimo", "packagesHighlight": "paketai", "packagesSubtitle": "Šie paketai yra apytiksliai klasifikavimai. Nemokamos konsultacijos metu padėsime nustatyti tikslų jūsų projektui reikalingą sudėtingumo lygį.", "starterTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "starterPrice": "€1,500 - €3,000", "starterDesc": "Puikiai tinka mažoms įmonėms, asmeniniams prekės ženklams ar paprastoms paslaugų įmonėms, kurioms reikia profesionalaus internetinio įvaizdžio.", "starterFeature1": "Iki 5 puslapių", "starterFeature2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "starterFeature3": "Pagrindinis SEO įdiegimas", "starterFeature4": "Kontaktų formos", "starterFeature5": "Socialinių tinklų integracija", "starterFeature6": "Pagrindinė analitika", "professionalTitle": "Profesionalus", "professionalPrice": "€3,000 - €7,000", "professionalDesc": "Idealiai tinka augančioms įmonėms, kurioms reikia daugiau funkcionalumo, g<PERSON><PERSON><PERSON><PERSON><PERSON> vartotojo patirties ir galim<PERSON><PERSON> pl<PERSON>.", "professionalFeature1": "Iki 15 puslapių", "professionalFeature2": "Pažangios <PERSON>", "professionalFeature3": "CMS integracija", "professionalFeature4": "E-komercijos <PERSON>", "professionalFeature5": "Pažangus SEO", "professionalFeature6": "<PERSON>ei<PERSON><PERSON> optimizavi<PERSON>", "professionalFeature7": "Trečiųjų šalių integracijos", "professionalFeature8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> integra<PERSON>", "enterpriseTitle": "Įmonės", "enterprisePrice": "€7,000+", "enterpriseDesc": "Įsitvirtinusioms įmonėms, kurioms reikia sudėtingo funkcionalumo, individualių integracijų ir įmonės lygio veikimo.", "enterpriseFeature1": "Neri<PERSON><PERSON> pu<PERSON>i", "enterpriseFeature2": "<PERSON><PERSON> kūrimas", "enterpriseFeature3": "API integracijos", "enterpriseFeature4": "Išskirtinio saug<PERSON> u<PERSON> ", "enterpriseFeature5": "Skirtingų kalbų integracija", "enterpriseFeature6": "Individualūs administravimo sky<PERSON>i", "enterpriseFeature7": "<PERSON><PERSON><PERSON><PERSON>", "enterpriseFeature8": "24/7 budėjimas", "getQuote": "<PERSON><PERSON><PERSON>", "whyChooseTitle": "Kod<PERSON>l rinktis UpZera jūsų svetainei?", "feature1": "Visiškai prisitaikantis <PERSON>", "feature2": "Greičiui optimizuotas veikimas", "feature3": "Tvirti SEO pagrindai", "feature4": "<PERSON><PERSON><PERSON> saugumo konfigūracija", "feature5": "<PERSON><PERSON><PERSON><PERSON> ir bend<PERSON><PERSON>tis procesas", "feature6": "Keičiami pagrindai ateities augimui", "ctaTitle": "Norite iš<PERSON>ti savo verslą ar idėją?", "ctaSubtitle": "<PERSON><PERSON><PERSON><PERSON>, kaip pritaik<PERSON> internetinis sprendimas gali skatinti augimą.", "ctaButton": "Užsisakykite nemokamą strategijos skambutį!", "workflowTitle": "Mūsų DI pagerintas darbo procesas", "workflowSubtitle": "Žmo<PERSON><PERSON> v<PERSON>, įrankiais padeda<PERSON> — greitesniems, protingesniems, geresniems rezultatams.", "lightningFastTitle": "Žaibiškai greitas", "lightningFastDesc": "Produktas gali būti pristat<PERSON> per dienas, o ne savaites su mūsų supaprastintu procesu, <PERSON><PERSON><PERSON>, priklausomai nuo užduoties sudėtingumo", "smartWorkflowsTitle": "<PERSON><PERSON>i darbo procesai", "smartWorkflowsDesc": "DI pagerintas kūrimas sumažina laiką ir padidina kokybę", "cleanDesignTitle": "<PERSON><PERSON><PERSON>", "cleanDesignDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, interak<PERSON><PERSON><PERSON><PERSON>, kurie p<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "seoOptimizedTitle": "SEO optimizuotas", "seoOptimizedDesc": "Sukurtas reitingavimui nuo pirmos dienos su išmaniais turinio p<PERSON>", "mobileFirstTitle": "Pirmenybė mobiliems įrenginiams ", "mobileFirstDesc": "P<PERSON><PERSON>ai prisitaikantis visuose įrenginiuose", "seamlessIntegrationsTitle": "Sklandžios integracijos", "seamlessIntegrationsDesc": "Pokal<PERSON><PERSON>, CRM ir daugiau integraijų kurios jungiasi be pastangų", "techStackTitle": "Mūsų technologijų rinkinys", "techStackDesc": "Mes naudo<PERSON>nes technologijas, kad sukurtume patiki<PERSON>, plečiamus ir auk<PERSON>to našumo internetinius sprendimus."}, "ChatbotIntegrationPage": {"mainTitle": "DI valdomo pokalbių roboto", "mainHighlight": "Integracija", "mainDescription": "Pagerinkite klientų patirtį, automatizuokite klientų aptarnavimą ir pritraukite naujus potencialius klientus 24/7 su protingais virtualiais asistentais, pritaikytais jūsų verslui.", "heroTitle": "Susipažinkite su savo", "heroHighlight": "<PERSON><PERSON> as<PERSON>", "getConsultation": "<PERSON><PERSON><PERSON> konsultaciją", "smartResponses": "<PERSON><PERSON><PERSON>", "leadCapture": "Potencialių klientų pritraukimas", "seamlessHandoff": "<PERSON><PERSON><PERSON><PERSON> perdavimas", "whyChooseTitle": "Kodėl pasirinkti UpZera virtualų asistentą?", "whyChooseDesc": "Mūsų DI asistentai yra kruopščiai sukurti siekiant pagerinti jūsų verslo veiklą ir suteikti išskirtinę vartotojo patirtį", "benefit1Title": "24/7 moment<PERSON><PERSON> p<PERSON>", "benefit1Desc": "Jūsų asistentas nieka<PERSON> nemiega — ji<PERSON> t<PERSON><PERSON>, DUK ir palaikymo p<PERSON>š<PERSON> bet kuriuo paros metu, gerinant at<PERSON><PERSON><PERSON> laik<PERSON> ir klientų pasitenkinimą.", "benefit2Title": "Potencialių klientų kvalifikavimas ir nukreipimas", "benefit2Desc": "<PERSON><PERSON> protingus, j<PERSON><PERSON><PERSON> sukalibruotus klausimus, filtru<PERSON> aukš<PERSON> kokyb<PERSON>s potencialius klientus ir nukreipia juos į tinkamą komandą ar platformą (CRM, el. paštas ir kt.) — nereikia rankinio rūšiavimo.", "benefit3Title": "Perda<PERSON><PERSON> gyvam agentui", "benefit3Desc": "Kai užklausa per sud<PERSON><PERSON>, jis gali sklandžiai perduoti pokalbį tikram <PERSON> — i<PERSON><PERSON><PERSON>nt visą kontekstą skland<PERSON>iam tęst<PERSON>ui.", "benefit4Title": "Personalizuota klientų patirtis", "benefit4Desc": "<PERSON><PERSON> pris<PERSON><PERSON>, pageidavimus ir konte<PERSON>, kad suteikt<PERSON> pritaikytus atsakymus, kurie atrodo natū<PERSON> ir na<PERSON>.", "benefit5Title": "Duo<PERSON><PERSON> ir įžvalgos", "benefit5Desc": "Kiekvienas pokalbis generuoja vertingus duomenis apie klientų poreikius, ska<PERSON><PERSON> ta<PERSON> ir el<PERSON><PERSON> model<PERSON>, kad informuotų jūsų verslo strategiją.", "benefit6Title": "Ekonomiš<PERSON> p<PERSON><PERSON>", "benefit6Desc": "Tvarkykite daugiau užklausų nesamdydami papildomų klientų aptarnavimo darbuotojų — jūsų virtualus asistentas auga kartu su jūsų verslo poreikiais.", "mvpTitle": "Minimalaus funkcionalumo virtualus asistentas", "mvpDesc": "<PERSON>š karto pradėkite su pagrindiniu virtualiu asistentu, pritaikytu jūsų esminėms verslo reikmėms. Idealiai tinka DUK automatizavimui ir pagrindiniam potencialių klientų pritraukimui.", "mvpFeature1": "Pagrindinis DUK automatizavimas", "mvpFeature2": "Potencialių klientų pritraukimo formos", "mvpFeature3": "El. p<PERSON><PERSON>", "mvpFeature4": "Paprasti pokalbių srautai", "mvpFeature5": "Pagrindinė analitika", "customTitle": "Pritaikomas DI asistentas", "customDesc": "Visiškai pritaikomas sprendimas su pažangiomis integracijomis (CRM, kalendorius), sudėtingais darbo srautais ir gilesniais konteksto suvokimo pokalbiais maksimaliam poveikiui.", "customFeature1": "Pažangus pokalbių DI", "customFeature2": "CRM integracijos", "customFeature3": "<PERSON><PERSON><PERSON><PERSON><PERSON> rezervavimas", "customFeature4": "Daug<PERSON><PERSON><PERSON><PERSON><PERSON>", "customFeature5": "<PERSON><PERSON><PERSON> darbo srautai", "customFeature6": "Pažangi analitika", "customFeature7": "Perda<PERSON><PERSON> gyvam agentui", "customFeature8": "API integracijos", "customPrice": "Nuo €2,000", "chooseSolutionTitle": "Pasirinkite savo sprendimą", "chooseSolutionSubtitle": "Pasirinkite tinkamą DI pagalbos lygį jūsų verslo poreikiams", "getStarted": "<PERSON><PERSON><PERSON><PERSON>", "ctaTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> transformuoti klientų sąveikas?", "ctaSubtitle": "Sukurkime DI asistentą, kuris puikiai veiks jūsų verslui.", "ctaButton": "Pradėkite savo chatboto kelionę"}}