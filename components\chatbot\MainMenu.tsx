import React from 'react';
import { ScreenSize } from './types';
import { useTranslations } from 'next-intl';

interface MenuOption {
  id: string;
  icon: string;
  text: string;
  description: string;
  message: string;
  intent: string;
}

interface MainMenuProps {
  onMenuClick: (message: string, intent: string) => void;
  screenSize: ScreenSize;
}

export default function MainMenu({ onMenuClick, screenSize }: MainMenuProps) {
  const t = useTranslations('Chatbot');
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust sizing based on screen size
  let buttonPadding = 'px-4 py-3';
  let buttonText = 'text-sm';
  let spacing = 'gap-3';
  let titleText = 'text-lg';

  if (isSmall) {
    if (width <= 320) {
      buttonPadding = 'px-3 py-2';
      buttonText = 'text-xs';
      spacing = 'gap-2';
      titleText = 'text-base';
    } else if (width <= 375) {
      buttonPadding = 'px-3 py-2';
      buttonText = 'text-xs';
      spacing = 'gap-2';
      titleText = 'text-base';
    } else {
      buttonPadding = 'px-4 py-3';
      buttonText = 'text-sm';
      spacing = 'gap-3';
      titleText = 'text-lg';
    }
  }

  const menuOptions: MenuOption[] = [
    {
      id: 'prices',
      icon: '💰',
      text: t('viewPricing'),
      description: t('viewPricingDesc'),
      message: t('mainMenuPricesMessage'),
      intent: 'prices'
    },
    {
      id: 'booking',
      icon: '📅',
      text: t('bookMeeting'),
      description: t('bookMeetingDesc'),
      message: t('mainMenuBookingMessage'),
      intent: 'booking'
    },
    {
      id: 'services',
      icon: '🚀',
      text: t('ourServices'),
      description: t('ourServicesDesc'),
      message: t('mainMenuServicesMessage'),
      intent: 'services'
    },
    {
      id: 'human_help',
      icon: '💬',
      text: t('talkToHuman'),
      description: t('talkToHumanDesc'),
      message: t('mainMenuHumanMessage'),
      intent: 'detailed_support'
    }
  ];

  const handleMenuClick = (option: MenuOption) => {
    onMenuClick(option.message, option.intent);
  };

  return (
    <div className="w-full animate-fade-in mb-4">
      <div className="bg-gray-50 rounded-lg p-3">
        <p className={`text-gray-600 mb-3 ${buttonText} text-center font-medium`}>
          {t('mainMenuGreeting')}
        </p>
        <div className={`space-y-2 ${spacing}`}>
          {menuOptions.map((option) => (
            <button
              key={option.id}
              onClick={() => handleMenuClick(option)}
              className={`
                w-full ${buttonPadding}
                bg-gradient-to-r from-pink-100 to-purple-100
                border border-pink-200
                rounded-full
                hover:from-pink-200 hover:to-purple-200
                hover:border-pink-300
                transition-all duration-200
                shadow-sm hover:shadow-md
                group
                ${buttonText}
                flex items-center justify-center
                min-h-[44px]
              `}
            >
              <span className="mr-2 text-lg group-hover:scale-110 transition-transform duration-200">
                {option.icon}
              </span>
              <span className="font-medium text-gray-800 group-hover:text-pink-600 transition-colors duration-200">
                {option.text}
              </span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
