"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card"; // Still needed for the Offerings Section

import { Clock, Filter, Users, CalendarCheck, HelpCircle, MessageSquareHeart, Bot, Zap } from "lucide-react";
import Link from "next/link";
// import Image from "next/image"; // Removed unused import
import { StarBorder } from "@/components/ui/star-border";
import { CheckmarkIcon } from "@/components/ui/checkmark-icon"; // Import the new icon

import { useTranslations } from 'next-intl';

// Import the FloatingAvatar component
import { FloatingAvatar } from '@/components/ui/floating-avatar';

export default function ChatbotIntegrationPage() {
  const t = useTranslations('ChatbotIntegrationPage');
  const benefits = [
    {
      icon: Clock,
      title: t('benefit1Title'),
      description: t('benefit1Desc'),
    },
    {
      icon: Filter,
      title: t('benefit2Title'),
      description: t('benefit2Desc'),
    },
    {
      icon: Users,
      title: t('benefit3Title'),
      description: t('benefit3Desc'),
    },
    {
      icon: CalendarCheck,
      title: t('benefit4Title'),
      description: t('benefit4Desc'),
    },
    {
      icon: HelpCircle,
      title: t('benefit5Title'),
      description: t('benefit5Desc'),
    },
    {
      icon: MessageSquareHeart,
      title: t('benefit6Title'),
      description: t('benefit6Desc'),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 text-white">
      {/* Hero Section with Integrated 3D Robot */}
      <div className="relative pt-32 pb-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="max-w-7xl mx-auto">
          {/* Main Title Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6">
              {t('mainTitle')} <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">{t('mainHighlight')}</span>
            </h1>
            <p className="text-lg md:text-xl text-purple-100/80 max-w-4xl mx-auto leading-relaxed">
              {t('mainDescription')}
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="space-y-8 lg:pl-12 order-2 lg:order-1">
              <h2 className="text-2xl lg:text-3xl font-bold mb-2">
                {t('heroTitle')} <span className="bg-gradient-to-r from-cyan-400 to-blue-500 text-transparent bg-clip-text">{t('heroHighlight')}</span>
              </h2>
              <div className="space-y-4">
                <Link href="/contact?service=chatbot">
                  <Button
                    size="default"
                    className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 w-full sm:w-auto transition-all duration-200 hover:scale-105 text-sm py-2 px-4"
                  >
                    {t('getConsultation')}
                  </Button>
                </Link>
                <div className="flex items-center gap-4 text-purple-100/60 text-sm">
                  <div className="flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    <span>{t('smartResponses')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Bot className="w-4 h-4" />
                    <span>{t('leadCapture')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    <span>{t('seamlessHandoff')}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Content - Floating Avatar */}
            <div className="relative h-[400px] md:h-[500px] lg:h-[550px] order-1 lg:order-2 flex items-center justify-center">
              {/* Enhanced background effects */}
              <div className="absolute inset-0 bg-gradient-radial from-pink-500/20 via-purple-500/15 to-transparent rounded-full blur-xl transform scale-90 z-0"></div>
              <div className="absolute -inset-10 bg-gradient-to-br from-purple-500/10 to-pink-500/5 rounded-full blur-3xl transform rotate-12"></div>

              <FloatingAvatar
                size="3xl"
                className="z-10"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="py-24 px-4 sm:px-6 lg:px-8 bg-purple-950/30 backdrop-blur-sm mt-8">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-4xl font-bold text-center mb-6 bg-gradient-to-r from-pink-600 to-purple-600 text-transparent bg-clip-text">
            {t('whyChooseTitle')}
          </h2>
          <p className="text-center text-purple-100/70 max-w-3xl mx-auto mb-16">
            {t('whyChooseDesc')}
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="bg-purple-900/40 backdrop-blur-sm rounded-xl p-8 border border-purple-700/30 hover:bg-purple-800/40 transition-all duration-300 hover:shadow-lg hover:shadow-purple-700/10 hover:-translate-y-1"
              >
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-12 h-12 rounded-lg flex items-center justify-center mb-6">
                  <benefit.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">{benefit.title}</h3>
                <p className="text-purple-100/80">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Offerings Section */}
      <div className="py-24 px-4 sm:px-6 lg:px-8 relative">
        {/* Background effect for the section */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,rgba(157,23,210,0.1),rgba(0,0,0,0))] opacity-60"></div>

        <div className="max-w-5xl mx-auto relative z-10">
          <div className="relative mb-20">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 blur-3xl opacity-50 -z-10 transform -translate-y-1/2"></div>
            <h2 className="text-5xl font-bold text-center mb-6 bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 text-transparent bg-clip-text">
              {t('chooseSolutionTitle')}
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-pink-500 mx-auto mb-6 rounded-full"></div>
            <p className="text-center text-purple-100/90 max-w-3xl mx-auto text-lg">
              {t('chooseSolutionSubtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-10 max-w-5xl mx-auto select-none">
            {/* MVP Option */}
            <Card
              className="bg-gradient-to-b from-blue-900/80 to-blue-800/60 backdrop-blur-sm rounded-xl p-10 border-2 border-blue-500/50 hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-300 hover:-translate-y-2 flex flex-col relative overflow-hidden"
              draggable="false"
              style={{ userSelect: 'none', touchAction: 'none' }}
            >
              {/* Background decoration */}
              <div className="absolute -top-24 -right-24 w-48 h-48 bg-blue-400/10 rounded-full blur-2xl"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-cyan-400/10 rounded-full blur-xl"></div>

              {/* Content */}
              <div className="relative z-10 pointer-events-auto">
                <div className="bg-gradient-to-br from-blue-400 to-cyan-400 w-20 h-20 rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-lg shadow-blue-500/20 transform hover:scale-105 transition-transform duration-300">
                  <Bot className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-center mb-4 text-white">{t('mvpTitle')}</h3>
                <div className="border-t-2 border-blue-500/30 w-1/3 mx-auto my-4"></div>
                <p className="text-blue-100/90 text-center mb-8 flex-grow text-lg">
                  {t('mvpDesc')}
                </p>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-center gap-3 text-blue-100/90 bg-blue-700/30 p-3 rounded-lg">
                    <div className="rounded-full bg-blue-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#38bdf8" />
                    </div>
                    <span className="font-medium">{t('mvpFeature1')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-blue-100/90 bg-blue-700/30 p-3 rounded-lg">
                    <div className="rounded-full bg-blue-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#38bdf8" />
                    </div>
                    <span className="font-medium">{t('mvpFeature2')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-blue-100/90 bg-blue-700/30 p-3 rounded-lg">
                    <div className="rounded-full bg-blue-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#38bdf8" />
                    </div>
                    <span className="font-medium">{t('mvpFeature3')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-blue-100/90 bg-blue-700/30 p-3 rounded-lg">
                    <div className="rounded-full bg-blue-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#38bdf8" />
                    </div>
                    <span className="font-medium">{t('mvpFeature4')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-blue-100/90 bg-blue-700/30 p-3 rounded-lg">
                    <div className="rounded-full bg-blue-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#38bdf8" />
                    </div>
                    <span className="font-medium">{t('mvpFeature5')}</span>
                  </li>
                </ul>
                <div className="text-center mb-6">
                  {/* <p className="text-2xl font-bold text-blue-200">{t('mvpPrice')}</p> */}
                </div>
                <Link href="/contact?service=chatbot-mvp" className="mt-auto">
                  <StarBorder className="w-full shadow-md shadow-blue-500/10 hover:shadow-lg hover:shadow-blue-500/20 transition-all duration-300">
                    <div className="relative z-1 border-none text-white text-center text-sm py-2 px-6 rounded-[18px] bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 transition-colors font-medium shadow-lg shadow-blue-500/20 hover:shadow-xl hover:shadow-blue-500/30 transition-all duration-300 hover:scale-[1.02]">
                      {t('getStarted')}
                    </div>
                  </StarBorder>
                </Link>
              </div>
            </Card>

            {/* Customizable Option */}
            <Card
              className="bg-gradient-to-b from-purple-900/80 to-pink-900/60 backdrop-blur-sm rounded-xl p-10 border-2 border-pink-500/50 hover:shadow-xl hover:shadow-pink-500/20 transition-all duration-300 hover:-translate-y-2 flex flex-col relative overflow-hidden"
              draggable="false"
              style={{ userSelect: 'none', touchAction: 'none' }}
            >
              {/* Background decoration */}
              <div className="absolute -top-24 -right-24 w-48 h-48 bg-pink-400/10 rounded-full blur-2xl"></div>
              <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-purple-400/10 rounded-full blur-xl"></div>

              {/* Content */}
              <div className="relative z-10 pointer-events-auto">
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-20 h-20 rounded-2xl flex items-center justify-center mb-6 mx-auto shadow-lg shadow-pink-500/20 transform hover:scale-105 transition-transform duration-300">
                  <MessageSquareHeart className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-center mb-4 text-white">{t('customTitle')}</h3>
                <div className="border-t-2 border-pink-500/30 w-1/3 mx-auto my-4"></div>
                <p className="text-pink-100/90 text-center mb-8 flex-grow text-lg">
                  {t('customDesc')}
                </p>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-center gap-3 text-pink-100/90 bg-pink-800/30 p-3 rounded-lg">
                    <div className="rounded-full bg-pink-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#ec4899" />
                    </div>
                    <span className="font-medium">{t('customFeature1')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-pink-100/90 bg-pink-800/30 p-3 rounded-lg">
                    <div className="rounded-full bg-pink-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#ec4899" />
                    </div>
                    <span className="font-medium">{t('customFeature2')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-pink-100/90 bg-pink-800/30 p-3 rounded-lg">
                    <div className="rounded-full bg-pink-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#ec4899" />
                    </div>
                    <span className="font-medium">{t('customFeature3')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-pink-100/90 bg-pink-800/30 p-3 rounded-lg">
                    <div className="rounded-full bg-pink-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#ec4899" />
                    </div>
                    <span className="font-medium">{t('customFeature4')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-pink-100/90 bg-pink-800/30 p-3 rounded-lg">
                    <div className="rounded-full bg-pink-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#ec4899" />
                    </div>
                    <span className="font-medium">{t('customFeature5')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-pink-100/90 bg-pink-800/30 p-3 rounded-lg">
                    <div className="rounded-full bg-pink-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#ec4899" />
                    </div>
                    <span className="font-medium">{t('customFeature6')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-pink-100/90 bg-pink-800/30 p-3 rounded-lg">
                    <div className="rounded-full bg-pink-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#ec4899" />
                    </div>
                    <span className="font-medium">{t('customFeature7')}</span>
                  </li>
                  <li className="flex items-center gap-3 text-pink-100/90 bg-pink-800/30 p-3 rounded-lg">
                    <div className="rounded-full bg-pink-500/30 p-1.5">
                      <CheckmarkIcon strokeColor="#ec4899" />
                    </div>
                    <span className="font-medium">{t('customFeature8')}</span>
                  </li>
                </ul>
                <div className="text-center mb-6">
                  {/* <p className="text-2xl font-bold text-pink-200">{t('customPrice')}</p> */}
                </div>
                <Link href="/contact?service=chatbot-custom" className="mt-auto">
                  <StarBorder className="w-full shadow-md shadow-pink-500/10 hover:shadow-lg hover:shadow-pink-500/20 transition-all duration-300">
                    <div className="relative z-1 border-none text-white text-center py-4 px-8 rounded-[18px] bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 transition-colors font-semibold shadow-lg shadow-purple-500/20 hover:shadow-xl hover:shadow-pink-500/30 transition-all duration-300 hover:scale-[1.02]">
                      {t('getStarted')}
                    </div>
                  </StarBorder>
                </Link>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
