"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Send } from "lucide-react";
import ClientOnly from "./ClientOnly";
import { useTranslations, useLocale } from 'next-intl';

interface NewsletterSubscribeProps {
  className?: string;
  buttonClassName?: string;
  inputClassName?: string;
  containerClassName?: string;
  buttonIcon?: boolean;
  buttonText?: string;
}

export default function NewsletterSubscribe({
  className = "",
  buttonClassName = "",
  inputClassName = "",
  containerClassName = "",
  buttonIcon = true,
  buttonText = ""
}: NewsletterSubscribeProps) {
  const t = useTranslations('Newsletter');
  const locale = useLocale();
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setSuccess(false);

    // Basic validation
    if (!email.trim()) {
      setError(t('emailRequired'));
      return;
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      setError(t('emailInvalid'));
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/newsletter", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, locale }),
      });

      if (!response.ok) {
        let errorMessage = "Failed to subscribe";
        try {
          const data = await response.json();
          errorMessage = data.error || errorMessage;
        } catch {
          // If response is not JSON, use default message
        }
        throw new Error(errorMessage);
      }

      await response.json(); // Consume the response
      setSuccess(true);
      setEmail("");
    } catch (err: any) {
      console.error('Newsletter subscription error:', err);
      setError(err.message || "Something went wrong. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fallback component for SSR
  const fallback = (
    <div className={`${containerClassName}`}>
      <form className={`flex gap-2 ${className}`}>
        <div className="flex-grow relative">
          <Input
            type="email"
            placeholder={t('emailPlaceholder')}
            value=""
            onChange={() => {}}
            className={`${inputClassName}`}
            disabled
          />
        </div>
        <Button
          type="button"
          className={`flex-shrink-0 ${buttonClassName}`}
          size={buttonIcon ? "icon" : "default"}
          aria-label="Subscribe"
          disabled
        >
          {buttonIcon ? <Send className="h-4 w-4" /> : buttonText || "Subscribe"}
        </Button>
      </form>
    </div>
  );

  return (
    <ClientOnly fallback={fallback}>
      <div className={`${containerClassName}`}>
        {success ? (
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <p className="text-green-700 font-medium">
              {t('successMessage')}
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className={`flex gap-2 ${className}`}>
            <div className="flex-grow relative">
              <Input
                type="email"
                placeholder={t('emailPlaceholder')}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className={`${inputClassName}`}
                disabled={isSubmitting}
              />
              {error && (
                <p className="text-red-500 text-xs absolute -bottom-5 left-0">
                  {error}
                </p>
              )}
            </div>
            <Button
              type="submit"
              className={`flex-shrink-0 ${buttonClassName}`}
              size={buttonIcon ? "icon" : "default"}
              aria-label="Subscribe"
              disabled={isSubmitting}
            >
              {buttonIcon ? <Send className="h-4 w-4" /> : buttonText || "Subscribe"}
            </Button>
          </form>
        )}
      </div>
    </ClientOnly>
  );
}
