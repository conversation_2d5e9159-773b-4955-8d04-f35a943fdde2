"use client";

import { useEffect, useState } from "react";

interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  imageLoadTime: number;
}

export function PerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const startTime = performance.now();
    
    // Monitor page load performance
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      
      entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming;
          setMetrics({
            loadTime: navEntry.loadEventEnd - navEntry.startTime,
            renderTime: navEntry.domContentLoadedEventEnd - navEntry.startTime,
            imageLoadTime: 0 // Will be updated by image load events
          });
        }
      });
    });

    observer.observe({ entryTypes: ['navigation'] });

    // Monitor image loading
    const images = document.querySelectorAll('img');
    let imageLoadStart = performance.now();
    let imagesLoaded = 0;
    
    images.forEach((img) => {
      if (img.complete) {
        imagesLoaded++;
      } else {
        img.addEventListener('load', () => {
          imagesLoaded++;
          if (imagesLoaded === images.length) {
            setMetrics(prev => prev ? {
              ...prev,
              imageLoadTime: performance.now() - imageLoadStart
            } : null);
          }
        });
      }
    });

    // Show metrics after 3 seconds
    const timer = setTimeout(() => setIsVisible(true), 3000);

    return () => {
      observer.disconnect();
      clearTimeout(timer);
    };
  }, []);

  if (!metrics || !isVisible) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs font-mono z-50">
      <div className="mb-1">📊 Performance Metrics</div>
      <div>Load: {Math.round(metrics.loadTime)}ms</div>
      <div>Render: {Math.round(metrics.renderTime)}ms</div>
      <div>Images: {Math.round(metrics.imageLoadTime)}ms</div>
      <button 
        onClick={() => setIsVisible(false)}
        className="mt-2 text-xs bg-white/20 px-2 py-1 rounded"
      >
        Hide
      </button>
    </div>
  );
}
