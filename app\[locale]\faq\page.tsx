"use client";

import React from 'react';
import { FaqSection } from "@/components/ui/faq-section";
import Link from 'next/link';
import { Button } from "@/components/ui/button";
import { Hourglass } from "lucide-react";
import { useTranslations } from 'next-intl';

export default function FAQPage() {
  const t = useTranslations('FAQ');
  const tCommon = useTranslations('Common');

  const UPZERA_FAQS = [
    {
      question: t('q1'),
      answer: t('a1')
    },
    {
      question: t('q2'),
      answer: t('a2')
    },
    {
      question: t('q3'),
      answer: t('a3')
    },
    {
      question: t('q4'),
      answer: t('a4')
    },
    {
      question: t('q5'),
      answer: t('a5')
    },
    {
      question: t('q6'),
      answer: t('a6')
    },
    {
      question: t('q7'),
      answer: t.raw('a7')
    },
    {
      question: t('q8'),
      answer: t.raw('a8')
    },
  ];

  return (
    <div className="min-h-screen pt-28 pb-20 bg-gradient-to-b from-purple-950 via-purple-900/95 to-purple-950 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <FaqSection
          title={t('title')}
          description={t('description')}
          items={UPZERA_FAQS}
          className="text-white w-full"
        />
      </div>

      {/* Bottom CTA Section with purple-pink gradient */}
      <section className="w-full py-20 mt-16 bg-gradient-to-r from-purple-700 via-purple-600 to-pink-500">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">
            {t('stillHaveQuestions')}
          </h2>
          <p className="text-xl text-white/90 max-w-3xl mx-auto mb-10">
            {t('stillHaveQuestionsDesc')}
          </p>

          <Link href="/contact">
            <Button
              className="w-full sm:w-auto bg-white/15 hover:bg-white/25 text-white border border-white/20
                         shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl text-lg px-4 py-3 sm:px-8 sm:py-6"
            >
              {tCommon('contactUs')}
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}
